#!/usr/bin/env python3
"""
Test the updated main function with processed folder and file existence checking
"""

from pathlib import Path
from extract_lab import main
import shutil

def test_processed_folder():
    """Test the main function with processed folder functionality"""
    
    # Create a test lab directory
    test_lab_dir = Path("test_lab_files")
    test_lab_dir.mkdir(exist_ok=True)
    
    # Copy the test file to our test lab directory
    source_file = Path("artifacts/001_labs.pdf")
    test_file = test_lab_dir / "001_labs.pdf"
    
    if source_file.exists():
        shutil.copy2(source_file, test_file)
        print(f"✓ Copied test file to: {test_file}")
    else:
        print(f"✗ Source file not found: {source_file}")
        return
    
    # Test 1: First run - should create processed folder and CSV
    print(f"\n" + "="*60)
    print("TEST 1: First run (should process file)")
    print("="*60)
    
    try:
        main(lab_files_path=test_lab_dir, explode_desc=False)
        
        # Check if processed folder was created
        processed_dir = test_lab_dir / "processed"
        expected_csv = processed_dir / "001_labs.csv"
        
        if processed_dir.exists():
            print(f"✓ Processed folder created: {processed_dir}")
        else:
            print(f"✗ Processed folder not created")
            
        if expected_csv.exists():
            print(f"✓ CSV file created: {expected_csv}")
            
            # Show basic info
            import pandas as pd
            df = pd.read_csv(expected_csv)
            print(f"  Shape: {df.shape}")
            print(f"  Columns: {list(df.columns)}")
        else:
            print(f"✗ CSV file not created")
            
    except Exception as e:
        print(f"✗ FAILED: Exception in first run: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 2: Second run - should skip existing file
    print(f"\n" + "="*60)
    print("TEST 2: Second run (should skip existing file)")
    print("="*60)
    
    try:
        main(lab_files_path=test_lab_dir, explode_desc=False)
        print("✓ Second run completed (should have skipped file)")
        
    except Exception as e:
        print(f"✗ FAILED: Exception in second run: {e}")
        import traceback
        traceback.print_exc()
    
    # Test 3: Add another file and run again
    print(f"\n" + "="*60)
    print("TEST 3: Add second file (should process only new file)")
    print("="*60)
    
    # Create a second test file (copy of the first)
    test_file2 = test_lab_dir / "002_labs.pdf"
    shutil.copy2(source_file, test_file2)
    print(f"✓ Added second test file: {test_file2}")
    
    try:
        main(lab_files_path=test_lab_dir, explode_desc=False)
        
        # Check if second CSV was created
        expected_csv2 = test_lab_dir / "processed" / "002_labs.csv"
        if expected_csv2.exists():
            print(f"✓ Second CSV file created: {expected_csv2}")
        else:
            print(f"✗ Second CSV file not created")
            
    except Exception as e:
        print(f"✗ FAILED: Exception in third run: {e}")
        import traceback
        traceback.print_exc()
    
    # Show final state
    print(f"\n" + "="*60)
    print("FINAL STATE:")
    print("="*60)
    
    processed_dir = test_lab_dir / "processed"
    if processed_dir.exists():
        csv_files = list(processed_dir.glob("*.csv"))
        print(f"✓ Processed folder contains {len(csv_files)} CSV file(s):")
        for csv_file in csv_files:
            print(f"  - {csv_file.name}")
    
    # Cleanup
    print(f"\nCleaning up test files...")
    try:
        shutil.rmtree(test_lab_dir)
        print("✓ Cleanup completed")
    except Exception as e:
        print(f"✗ Cleanup failed: {e}")


if __name__ == "__main__":
    test_processed_folder()

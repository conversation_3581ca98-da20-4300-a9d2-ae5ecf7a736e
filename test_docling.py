#!/usr/bin/env python3
"""
Test script to debug Docling table detection with the 001_labs.pdf file
"""

import json
import sys
from pathlib import Path

try:
    from docling.document_converter import DocumentConverter, PdfFormatOption
    from docling.datamodel.pipeline_options import PdfPipelineOptions
    from docling.datamodel.base_models import InputFormat
    import pandas as pd
except ImportError as e:
    print(f"Missing dependency: {e}")
    print("Please install: pip install docling pandas")
    sys.exit(1)


def test_basic_conversion(pdf_path: Path):
    """Test basic Docling conversion without custom options"""
    print(f"Testing basic conversion on: {pdf_path}")
    
    try:
        # Simple converter without custom options
        converter = DocumentConverter()
        result = converter.convert(pdf_path)
        doc = result.document
        
        print(f"✓ Basic conversion successful")
        print(f"  Document type: {type(doc)}")
        print(f"  Has elements: {hasattr(doc, 'elements')}")
        print(f"  Has tables: {hasattr(doc, 'tables')}")
        
        # Check for elements
        if hasattr(doc, 'elements'):
            elements = doc.elements
            print(f"  Number of elements: {len(elements) if elements else 0}")
            
            table_elements = []
            for i, el in enumerate(elements or []):
                el_type = getattr(el, 'type', 'unknown')
                print(f"    Element {i}: {el_type}")
                if 'table' in str(el_type).lower():
                    table_elements.append(el)
            
            print(f"  Table elements found: {len(table_elements)}")
            
        # Check for tables attribute
        if hasattr(doc, 'tables'):
            tables = doc.tables
            print(f"  Direct tables attribute: {len(tables) if tables else 0}")
            
        return doc
        
    except Exception as e:
        print(f"✗ Basic conversion failed: {e}")
        return None


def test_with_options(pdf_path: Path):
    """Test conversion with PDF pipeline options"""
    print(f"\nTesting conversion with options on: {pdf_path}")
    
    try:
        # Configure with options
        pdf_pipeline_options = PdfPipelineOptions()
        converter = DocumentConverter(
            format_options={
                InputFormat.PDF: PdfFormatOption(pipeline_options=pdf_pipeline_options)
            }
        )
        result = converter.convert(pdf_path)
        doc = result.document
        
        print(f"✓ Options-based conversion successful")
        return doc
        
    except Exception as e:
        print(f"✗ Options-based conversion failed: {e}")
        return None


def test_table_extraction_methods(doc):
    """Test different methods to extract tables from the document"""
    print(f"\nTesting table extraction methods...")
    
    if doc is None:
        print("No document to test")
        return []
    
    dfs = []
    
    # Method 1: Check elements for tables
    if hasattr(doc, 'elements'):
        print("Method 1: Checking elements...")
        elements = doc.elements or []
        for i, el in enumerate(elements):
            el_type = getattr(el, 'type', '')
            if 'table' in str(el_type).lower():
                print(f"  Found table element {i}: {el_type}")
                
                # Try different export methods
                df = None
                for method_name in ["to_pandas", "to_dataframe", "export_to_dataframe", "export_to_pandas"]:
                    if hasattr(el, method_name):
                        try:
                            df = getattr(el, method_name)()
                            print(f"    ✓ Exported via {method_name}: {df.shape}")
                            break
                        except Exception as e:
                            print(f"    ✗ Failed {method_name}: {e}")
                
                if df is not None and isinstance(df, pd.DataFrame):
                    dfs.append(df)
    
    # Method 2: Check doc.tables directly
    if hasattr(doc, 'tables'):
        print("Method 2: Checking doc.tables...")
        tables = doc.tables or []
        for i, tbl in enumerate(tables):
            print(f"  Found table {i}: {type(tbl)}")
            
            df = None
            for method_name in ["to_pandas", "to_dataframe", "export_to_dataframe", "export_to_pandas"]:
                if hasattr(tbl, method_name):
                    try:
                        df = getattr(tbl, method_name)()
                        print(f"    ✓ Exported via {method_name}: {df.shape}")
                        break
                    except Exception as e:
                        print(f"    ✗ Failed {method_name}: {e}")
            
            if df is not None and isinstance(df, pd.DataFrame):
                dfs.append(df)
    
    # Method 3: Try document-level export
    print("Method 3: Checking document-level exports...")
    for method_name in ["export_to_dataframes", "to_pandas", "get_tables"]:
        if hasattr(doc, method_name):
            try:
                result = getattr(doc, method_name)()
                print(f"  ✓ Document {method_name}: {type(result)}")
                if isinstance(result, list):
                    for df in result:
                        if isinstance(df, pd.DataFrame):
                            dfs.append(df)
                elif isinstance(result, pd.DataFrame):
                    dfs.append(result)
            except Exception as e:
                print(f"  ✗ Failed {method_name}: {e}")
    
    print(f"\nTotal DataFrames extracted: {len(dfs)}")
    for i, df in enumerate(dfs):
        print(f"  DataFrame {i}: {df.shape} - Columns: {list(df.columns)}")
    
    return dfs


def main():
    test_file = Path("artifacts/001_labs.pdf")
    
    if not test_file.exists():
        print(f"Test file not found: {test_file}")
        sys.exit(1)
    
    print(f"Testing Docling table extraction with: {test_file}")
    print("=" * 60)
    
    # Test basic conversion
    doc1 = test_basic_conversion(test_file)
    
    # Test with options
    doc2 = test_with_options(test_file)
    
    # Test table extraction on the successful document
    doc = doc1 or doc2
    if doc:
        dfs = test_table_extraction_methods(doc)
        
        if dfs:
            print(f"\n✓ SUCCESS: Found {len(dfs)} table(s)")
            # Save first table as test output
            if dfs[0] is not None and not dfs[0].empty:
                output_path = Path("test_output.csv")
                dfs[0].to_csv(output_path, index=False)
                print(f"  Saved first table to: {output_path}")
        else:
            print(f"\n✗ FAILED: No tables extracted")
    else:
        print(f"\n✗ FAILED: Could not convert document")


if __name__ == "__main__":
    main()

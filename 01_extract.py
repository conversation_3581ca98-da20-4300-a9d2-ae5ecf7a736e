# 01_extract.py
import re, json, csv
from pathlib import Path
from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
import pandas as pd

from config import LAB_DIR, DX_DIR, LAB_NORMALIZATION, FEATURES

# --- PDF text helpers ---------------------------------------------------------
def _read_pdf_text(path: Path) -> str:
    text = ""
    try:
        import pdfplumber
        with pdfplumber.open(str(path)) as pdf:
            for page in pdf.pages:
                txt = page.extract_text() or ""
                text += "\n" + txt
        if text.strip():
            return text
    except Exception:
        pass
    # Fallback: PyPDF2
    try:
        from PyPDF2 import PdfReader
        reader = PdfReader(str(path))
        for page in reader.pages:
            text += "\n" + (page.extract_text() or "")
    except Exception:
        pass
    return text

def _read_any_text(path: Path) -> str:
    if path.suffix.lower() == ".pdf":
        return _read_pdf_text(path)
    elif path.suffix.lower() in {".txt", ".md"}:
        return path.read_text(errors="ignore")
    elif path.suffix.lower() in {".json"}:
        return json.dumps(json.loads(path.read_text(errors="ignore")))
    elif path.suffix.lower() in {".csv"}:
        return "\n".join(",".join(row) for row in csv.reader(open(path, newline="", encoding="utf-8", errors="ignore")))
    return ""

# --- Normalization utilities ---------------------------------------------------
def _normalize_key(s: str) -> str:
    return re.sub(r"[^a-z0-9% ]+", "", s.lower()).strip()

def _find_canonical_key(raw_key: str) -> Optional[str]:
    k = _normalize_key(raw_key)
    # Avoid matching very short keys that could be ambiguous
    if len(k) < 2:
        return None

    for canon, variants in LAB_NORMALIZATION.items():
        for v in variants:
            v_norm = _normalize_key(v)
            # Require a more exact match to avoid false positives
            # Either the normalized key contains the variant, or vice versa
            # But avoid partial matches that are too short
            if (v_norm in k and len(v_norm) >= 2) or (k in v_norm and len(k) >= 2):
                # Additional check: avoid matching "na" in "national"
                if v_norm == "na" and "national" in k:
                    continue
                return canon
    return None

def _to_number(token: str) -> Optional[float]:
    token = token.strip()
    # handle negatives, commas, weird unicode
    token = token.replace(",", "")
    m = re.search(r"[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?", token)
    if not m:
        return None
    try:
        return float(m.group(0))
    except Exception:
        return None

# --- Lab parser (robust for "Name Units Result Reference" and "Key: Val") -----
def parse_labs(text: str) -> Dict[str, float]:
    feats: Dict[str, float] = {}
    lines = [ln.strip() for ln in text.splitlines() if ln.strip()]

    for ln in lines:
        # Try table-like rows: e.g., "Hemoglobin (Hb) g/dL 14.995 12.0–17.5 g/dL"
        # Pattern: Test_Name Units Result Reference_Range

        # First, try to parse as a lab result table row
        # Look for lines that have a test name followed by units and numeric values
        if any(unit in ln.lower() for unit in ['g/dl', '%', 'mg/dl', 'mmol/l', 'µl', '/µl', 'sec', 'pg', 'fl']):
            # Split by spaces and try to identify the structure
            parts = ln.split()
            if len(parts) >= 3:
                # Find the test name (usually the first part, possibly with parentheses)
                test_name_parts = []
                units_start_idx = -1

                # Look for the units (like g/dL, %, etc.)
                for i, part in enumerate(parts):
                    if any(unit in part.lower() for unit in ['g/dl', '%', 'mg/dl', 'mmol/l', 'µl', '/µl', 'sec', 'pg', 'fl']):
                        units_start_idx = i
                        break

                if units_start_idx > 0:
                    # Test name is everything before the units
                    test_name = ' '.join(parts[:units_start_idx])
                    # Result should be the first numeric value after units
                    remaining_parts = parts[units_start_idx + 1:]

                    for part in remaining_parts:
                        result = _to_number(part)
                        if result is not None:
                            canon = _find_canonical_key(test_name)
                            if canon:
                                feats.setdefault(canon, result)
                            break

        # Also support "Key: value" style, but be more restrictive
        m = re.match(r"(.+?):\s*(.+)$", ln)
        if m:
            key_part = m.group(1).strip()
            val_part = m.group(2).strip()

            # Only process if the key looks like a lab test name
            # Avoid things like "Patient Name", "National ID", "Address", etc.
            if not any(skip in key_part.lower() for skip in ['patient', 'name', 'id', 'address', 'date', 'physician', 'doctor']):
                canon = _find_canonical_key(key_part)
                val = _to_number(val_part)
                if canon and val is not None:
                    feats.setdefault(canon, val)

    # Attempt to extract Age if present anywhere
    m_age = re.search(r"\bage\s*:\s*(\d{1,3})\b", text, flags=re.I)
    if m_age:
        feats["age"] = float(m_age.group(1))
    else:
        # backup: "Age 67" pattern
        m_age2 = re.search(r"\bage\D+(\d{1,3})\b", text, flags=re.I)
        if m_age2:
            feats["age"] = float(m_age2.group(1))

    return feats

# --- Diagnosis parser (bulleted or comma-separated after 'Diagnoses:') --------
def parse_diagnoses(text: str) -> List[str]:
    # Find the block after "Diagnoses:" until blank line or end
    m = re.search(r"diagnoses\s*:\s*(.*)", text, flags=re.I | re.S)
    if not m:
        return []
    tail = m.group(1)
    # Split bullets or newlines
    raw = re.split(r"[\n\r\u2022\-•]+", tail)
    labels = []
    for item in raw:
        item = item.strip(" .;:\t").lower()
        if not item:
            continue
        # stop if we hit an unrelated section header
        if re.match(r"^(lifestyle|plan|assessment|follow[- ]?up|reason for visit|allerg|medication)\b", item):
            break
        # keep only short-ish diagnosis phrases
        if 2 <= len(item) <= 80:
            labels.append(item)
    # de-duplicate & clean
    return sorted(set(labels))

# --- Pairing files by stem -----------------------------------------------------
def _index_by_stem(folder: Path) -> Dict[str, Path]:
    """Index files by their numeric prefix (e.g., '001' from '001_labs.pdf')"""
    idx = {}
    for p in folder.glob("*"):
        if p.is_file() and p.suffix.lower() in {".pdf", ".txt", ".json", ".csv"}:
            # Extract numeric prefix from filename
            match = re.match(r'(\d+)', p.stem)
            if match:
                numeric_key = match.group(1)
                idx[numeric_key] = p
    return idx

def main():
    labs = _index_by_stem(LAB_DIR)
    dxs = _index_by_stem(DX_DIR)
    shared = sorted(set(labs) & set(dxs))
    rows = []
    for stem in shared:
        lab_text = _read_any_text(labs[stem])
        dx_text  = _read_any_text(dxs[stem])

        feats = parse_labs(lab_text)
        labels = parse_diagnoses(dx_text)

        # Keep only known features; missing handled later
        row = {"id": stem}
        for f in FEATURES:
            row[f] = feats.get(f, None)
        row["labels"] = "|".join(labels)  # multi-label string
        rows.append(row)

    df = pd.DataFrame(rows)
    out = Path("artifacts"); out.mkdir(exist_ok=True)
    df.to_csv(out / "dataset.csv", index=False)
    print(f"Wrote {len(df)} paired examples to artifacts/dataset.csv")

if __name__ == "__main__":
    main()

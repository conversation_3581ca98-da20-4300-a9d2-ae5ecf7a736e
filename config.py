# config.py
from pathlib import Path

DATA_DIR = Path("files")
LAB_DIR = Path("/Users/<USER>/Documents/APPS/meadna/labs_1")
DX_DIR = Path("/Users/<USER>/Documents/APPS/meadna/diagnoses")

# Canonical lab feature names and their common variants found in PDFs/CSVs.
# Keys are the normalized column names we’ll use in the dataset.
LAB_NORMALIZATION = {
    "age": ["age"],
    "hemoglobin": ["hemoglobin", "hb"],
    "hematocrit": ["hematocrit", "hct"],
    "rbc": ["red blood cell count", "rbc"],
    "mcv": ["mean corpuscular volume", "mcv"],
    "mch": ["mean corpuscular hemoglobin", "mch"],
    "mchc": ["mean corpuscular hemoglobin concentration", "mchc"],
    "wbc": ["white blood cell count", "wbc"],
    "platelets": ["platelet count", "plt"],
    "neutrophils_pct": ["neutrophils %", "neutrophils"],
    "lymphocytes_pct": ["lymphocytes %", "lymphocytes"],
    "pt": ["prothrombin time", "pt"],
    "inr": ["international normalized ratio", "inr"],
    "aptt": ["activated partial thromboplastin time", "aptt", "aPTT"],
    "creatinine": ["creatinine"],
    "bun": ["blood urea nitrogen", "bun"],
    "sodium": ["sodium", "na"],
    "potassium": ["potassium", "k"],
    "chloride": ["chloride", "cl"],
    "calcium": ["calcium", "ca"],
    "phosphate": ["phosphate", "po4", "po\u00b3\u207b", "po3"],
    "alt": ["alanine transaminase", "alt"],
    "ast": ["aspartate transaminase", "ast"],
    "alp": ["alkaline phosphatase", "alp"],
    "ggt": ["gamma-glutamyl transferase", "ggt"],
    "bilirubin_total": ["bilirubin (total)", "bilirubin"],
    "albumin": ["albumin"],
    "glucose_fasting": ["glucose (fasting)", "glucose"],
    "hba1c": ["hemoglobin a1c", "hba1c"],
    "cholesterol_total": ["cholesterol (total)", "cholesterol total", "cholesterol"],
    "ldl_c": ["ldl-c", "ldl"],
    "hdl_c": ["hdl-c", "hdl"],
    "triglycerides": ["triglycerides"],
    "crp": ["c-reactive protein", "crp"],
    "ferritin": ["ferritin"],
    "vitamin_d_25oh": ["vitamin d (25-oh)", "vitamin d"],
    "tsh": ["thyroid stimulating hormone", "tsh"],
    "ft4": ["free t4", "ft4"],
    "homocysteine": ["homocysteine"],
    "troponin_hs": ["troponin (hs)", "troponin"],
    "nt_probnp": ["nt-probnp", "nt-probnp", "nt-proBNP"],
}
# Feature list order (you can trim further if desired)
FEATURES = list(LAB_NORMALIZATION.keys())
RANDOM_STATE = 42

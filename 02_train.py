# 02_train.py
import json
from pathlib import Path
import numpy as np
import pandas as pd
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler, MultiLabelBinarizer
from sklearn.impute import SimpleImputer
from sklearn.linear_model import LogisticRegression
from sklearn.multiclass import OneVsRestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, f1_score, roc_auc_score
from joblib import dump
from config import FEATURES, RANDOM_STATE

def load_dataset(path: Path) -> pd.DataFrame:
    df = pd.read_csv(path)
    # Split the pipe-delimited labels into lists
    df["label_list"] = df["labels"].fillna("").apply(lambda s: [x for x in s.split("|") if x])
    return df

def main():
    ds_path = Path("artifacts/dataset.csv")
    df = load_dataset(ds_path)

    # Filter rows with at least one label
    df = df[df["label_list"].map(len) > 0].reset_index(drop=True)

    X = df[FEATURES].copy()
    y_raw = df["label_list"].tolist()

    mlb = MultiLabelBinarizer()
    Y = mlb.fit_transform(y_raw)
    classes = mlb.classes_.tolist()

    X_train, X_val, Y_train, Y_val = train_test_split(
        X, Y, test_size=0.2, random_state=RANDOM_STATE, stratify=(Y.sum(axis=1) > 0)
    )

    model = Pipeline([
        ("imputer", SimpleImputer(strategy="median")),
        ("scaler", StandardScaler(with_mean=False)),  # safe with sparse
        ("clf", OneVsRestClassifier(
            LogisticRegression(max_iter=1000, class_weight="balanced", n_jobs=None)
        )),
    ])

    model.fit(X_train, Y_train)

    # Validation
    Y_prob = model.predict_proba(X_val)
    Y_pred = (Y_prob >= 0.5).astype(int)

    # Metrics
    report = classification_report(Y_val, Y_pred, target_names=classes, output_dict=True, zero_division=0)
    micro_f1 = f1_score(Y_val, Y_pred, average="micro", zero_division=0)
    macro_f1 = f1_score(Y_val, Y_pred, average="macro", zero_division=0)

    # Some datasets may make ROC-AUC tricky if a class has only one label in val set.
    try:
        micro_auc = roc_auc_score(Y_val, Y_prob, average="micro")
    except Exception:
        micro_auc = None

    artifacts = Path("artifacts"); artifacts.mkdir(exist_ok=True)
    dump(model, artifacts / "model.joblib")
    dump(mlb, artifacts / "labels_mlb.joblib")

    with open(artifacts / "metrics.json", "w") as f:
        json.dump({
            "classes": classes,
            "micro_f1": micro_f1,
            "macro_f1": macro_f1,
            "micro_auc": micro_auc,
            "per_class": report,
        }, f, indent=2)

    print(f"Saved model + label binarizer to artifacts/, micro_f1={micro_f1:.3f}, macro_f1={macro_f1:.3f}")

if __name__ == "__main__":
    main()

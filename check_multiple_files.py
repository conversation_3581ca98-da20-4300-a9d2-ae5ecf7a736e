#!/usr/bin/env python3

from pathlib import Path
from config import LAB_DIR
import importlib.util

# Import from 01_extract.py
spec = importlib.util.spec_from_file_location("extract", "01_extract.py")
extract_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(extract_module)

_read_any_text = extract_module._read_any_text
parse_labs = extract_module.parse_labs

def check_multiple_files():
    """Check extraction results across multiple files"""
    
    for i in range(1, 4):  # Check first 3 files
        file_path = LAB_DIR / f"{i:03d}_labs.pdf"
        if not file_path.exists():
            continue
            
        print(f"\n=== File {file_path.name} ===")
        text = _read_any_text(file_path)
        feats = parse_labs(text)
        
        # Show key lab values
        key_labs = ['age', 'hemoglobin', 'cholesterol_total', 'ldl_c', 'hdl_c', 'triglycerides']
        for lab in key_labs:
            if lab in feats:
                value = feats[lab]
                # Flag potentially problematic values
                flag = ""
                if lab == 'cholesterol_total' and (value < 0 or value > 500):
                    flag = " ⚠️ UNUSUAL"
                elif lab in ['ldl_c', 'hdl_c'] and (value < 0 or value > 300):
                    flag = " ⚠️ UNUSUAL"
                elif lab == 'hemoglobin' and (value < 5 or value > 20):
                    flag = " ⚠️ UNUSUAL"
                    
                print(f"  {lab}: {value}{flag}")
        
        # Look for cholesterol lines in raw text
        lines = text.splitlines()
        for line in lines:
            if 'cholesterol' in line.lower():
                print(f"  Raw cholesterol line: {line}")

def main():
    check_multiple_files()

if __name__ == "__main__":
    main()

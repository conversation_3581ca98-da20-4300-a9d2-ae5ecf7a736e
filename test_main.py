#!/usr/bin/env python3
"""
Test the main function with a single file to verify it works correctly
"""

from pathlib import Path
from extract_lab import main

def test_main_function():
    """Test the main function with the artifacts directory"""
    
    # Create a temporary test directory with just our test file
    test_dir = Path("test_lab_dir")
    test_dir.mkdir(exist_ok=True)
    
    # Copy the test file to our test directory
    import shutil
    source_file = Path("artifacts/001_labs.pdf")
    test_file = test_dir / "001_labs.pdf"
    
    if source_file.exists():
        shutil.copy2(source_file, test_file)
        print(f"Copied test file to: {test_file}")
    else:
        print(f"Source file not found: {source_file}")
        return
    
    # Test the main function
    output_dir = Path("main_test_outputs")
    
    print(f"\nTesting main() function...")
    print(f"Input dir: {test_dir}")
    print(f"Output dir: {output_dir}")
    
    try:
        # Call main with our test directory
        main(lab_files_path=test_dir, out_dir=output_dir, explode_desc=False)
        
        # Check if output was created
        expected_output = output_dir / "001_labs.csv"
        if expected_output.exists():
            print(f"\n✓ SUCCESS: Output created at {expected_output}")
            
            # Show basic info about the output
            import pandas as pd
            df = pd.read_csv(expected_output)
            print(f"  Shape: {df.shape}")
            print(f"  Columns: {list(df.columns)}")
            
            # Show description sample
            if 'description_json' in df.columns:
                import json
                desc = json.loads(df['description_json'].iloc[0])
                print(f"  Description fields: {list(desc.keys())}")
                print(f"  Patient Name: {desc.get('Patient Name', 'N/A')}")
                print(f"  Lab Date: {desc.get('Lab Date', 'N/A')}")
        else:
            print(f"✗ FAILED: Expected output not found at {expected_output}")
            
    except Exception as e:
        print(f"✗ FAILED: Exception in main(): {e}")
        import traceback
        traceback.print_exc()
    
    # Cleanup
    try:
        if test_file.exists():
            test_file.unlink()
        if test_dir.exists():
            test_dir.rmdir()
    except Exception:
        pass


if __name__ == "__main__":
    test_main_function()

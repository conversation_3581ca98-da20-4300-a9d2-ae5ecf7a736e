#!/usr/bin/env python3

from pathlib import Path
from config import LAB_DIR
import importlib.util
import sys

# Import from 01_extract.py
spec = importlib.util.spec_from_file_location("extract", "01_extract.py")
extract_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(extract_module)

_read_any_text = extract_module._read_any_text
parse_labs = extract_module.parse_labs

def debug_specific_extractions():
    """Debug specific problematic extractions"""
    sample_file = LAB_DIR / "001_labs.pdf"
    if not sample_file.exists():
        print(f"Sample file not found: {sample_file}")
        return
    
    text = _read_any_text(sample_file)
    lines = [ln.strip() for ln in text.splitlines() if ln.strip()]
    
    # Look for lines that might contain cholesterol, ldl, hdl values
    print("=== Looking for cholesterol-related lines ===")
    for i, line in enumerate(lines):
        if any(term in line.lower() for term in ['cholesterol', 'ldl', 'hdl', 'triglyceride']):
            print(f"Line {i+1}: {line}")
    
    print("\n=== Looking for lines with negative numbers ===")
    import re
    for i, line in enumerate(lines):
        if re.search(r'-\d+\.?\d*', line):
            print(f"Line {i+1}: {line}")
    
    print("\n=== Full text around cholesterol section ===")
    # Find the section with cholesterol values
    for i, line in enumerate(lines):
        if 'cholesterol' in line.lower():
            start = max(0, i-2)
            end = min(len(lines), i+5)
            print(f"Context around line {i+1}:")
            for j in range(start, end):
                marker = ">>> " if j == i else "    "
                print(f"{marker}{j+1:2d}: {lines[j]}")
            print()

def main():
    debug_specific_extractions()

if __name__ == "__main__":
    main()

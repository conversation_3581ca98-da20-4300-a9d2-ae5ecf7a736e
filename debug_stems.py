#!/usr/bin/env python3

from pathlib import Path
from config import LAB_DIR, DX_DIR

def debug_index_by_stem(folder: Path, name: str):
    """Index files by their numeric prefix (e.g., '001' from '001_labs.pdf')"""
    print(f"\n=== Debugging {name} ===")
    print(f"Folder: {folder}")
    print(f"Exists: {folder.exists()}")

    if not folder.exists():
        return {}

    idx = {}
    count = 0
    import re
    for p in folder.glob("*"):
        if p.is_file() and p.suffix.lower() in {".pdf", ".txt", ".json", ".csv"}:
            # Extract numeric prefix from filename
            match = re.match(r'(\d+)', p.stem)
            if match:
                numeric_key = match.group(1)
                idx[numeric_key] = p
                count += 1
                if count <= 5:  # Show first 5 examples
                    print(f"  File: {p.name} -> Numeric key: {numeric_key}")

    print(f"Total files indexed: {len(idx)}")
    return idx

def main():
    labs = debug_index_by_stem(LAB_DIR, "LABS")
    dxs = debug_index_by_stem(DX_DIR, "DIAGNOSES")
    
    print(f"\n=== MATCHING ===")
    print(f"Lab stems (first 5): {sorted(list(labs.keys()))[:5]}")
    print(f"Dx stems (first 5): {sorted(list(dxs.keys()))[:5]}")
    
    shared = sorted(set(labs) & set(dxs))
    print(f"Shared stems: {len(shared)}")
    if shared:
        print(f"First 5 shared: {shared[:5]}")
    else:
        print("No shared stems found!")
        
        # Let's see if we can find a pattern
        lab_prefixes = set()
        dx_prefixes = set()
        
        for stem in list(labs.keys())[:10]:
            # Try to extract number prefix
            import re
            match = re.match(r'(\d+)', stem)
            if match:
                lab_prefixes.add(match.group(1))
        
        for stem in list(dxs.keys())[:10]:
            match = re.match(r'(\d+)', stem)
            if match:
                dx_prefixes.add(match.group(1))
        
        print(f"Lab number prefixes: {sorted(lab_prefixes)}")
        print(f"Dx number prefixes: {sorted(dx_prefixes)}")
        
        common_prefixes = lab_prefixes & dx_prefixes
        print(f"Common number prefixes: {sorted(common_prefixes)}")

if __name__ == "__main__":
    main()

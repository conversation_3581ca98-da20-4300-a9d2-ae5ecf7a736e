# 04_infer.py
import sys
from pathlib import Path
from joblib import load
from config import LAB_DIR, LAB_NORMALIZATION, FEATURES
import re
from typing import Optional

def _read_pdf_text(path: Path) -> str:
    try:
        import pdfplumber
        with pdfplumber.open(str(path)) as pdf:
            return "\n".join([(p.extract_text() or "") for p in pdf.pages])
    except Exception:
        pass
    try:
        from PyPDF2 import PdfReader
        reader = PdfReader(str(path))
        return "\n".join([(p.extract_text() or "") for p in reader.pages])
    except Exception:
        return ""

def _normalize_key(s: str) -> str:
    return re.sub(r"[^a-z0-9% ]+", "", s.lower()).strip()

def _find_canonical_key(raw_key: str) -> Optional[str]:
    k = _normalize_key(raw_key)
    for canon, variants in LAB_NORMALIZATION.items():
        for v in variants:
            if _normalize_key(v) in k or k in _normalize_key(v):
                return canon
    return None

def _to_number(token: str):
    import re
    token = token.replace(",", "")
    m = re.search(r"[-+]?\d*\.?\d+(?:[eE][-+]?\d+)?", token)
    return float(m.group(0)) if m else None

def parse_labs(text: str):
    feats = {}
    for ln in text.splitlines():
        ln = ln.strip()
        if not ln:
            continue
        parts = re.split(r"\s{2,}|\t", ln)
        if len(parts) >= 2:
            nums = [p for p in parts if _to_number(p) is not None]
            result = _to_number(nums[0]) if nums else None
            canon = _find_canonical_key(parts[0])
            if canon and result is not None:
                feats.setdefault(canon, result)
        m = re.match(r"(.+?):\s*(.+)$", ln)
        if m:
            canon = _find_canonical_key(m.group(1))
            val = _to_number(m.group(2))
            if canon and val is not None:
                feats.setdefault(canon, val)
    return feats

def main():
    if len(sys.argv) < 2:
        print("Usage: python 04_infer.py files/labs/123.pdf")
        sys.exit(1)
    lab_path = Path(sys.argv[1])
    text = _read_pdf_text(lab_path)
    feats = parse_labs(text)
    x = [[feats.get(f, None) for f in FEATURES]]

    from sklearn.impute import SimpleImputer
    import numpy as np
    x = SimpleImputer(strategy="median").fit_transform(x)

    model = load(Path("artifacts/model.joblib"))
    mlb = load(Path("artifacts/labels_mlb.joblib"))

    probs = model.predict_proba(x)[0]
    indices = probs.argsort()[::-1][:10]
    for i in indices:
        print(f"{mlb.classes_[i]}: {probs[i]:.3f}")

if __name__ == "__main__":
    main()

# 03_validate.py
import json
import numpy as np
from pathlib import Path
import pandas as pd
from joblib import load
from sklearn.metrics import f1_score, precision_score

from config import FEATURES, RANDOM_STATE

def precision_at_k(y_true, y_prob, k=3):
    # Take top-k labels per sample
    topk = np.argsort(-y_prob, axis=1)[:, :k]
    pred = np.zeros_like(y_prob, dtype=int)
    rows = np.arange(y_prob.shape[0])[:, None]
    pred[rows, topk] = 1
    return precision_score(y_true, pred, average="samples", zero_division=0)

def main():
    artifacts = Path("artifacts")
    df = pd.read_csv(artifacts / "dataset.csv")
    df["label_list"] = df["labels"].fillna("").apply(lambda s: [x for x in s.split("|") if x])

    X = df[FEATURES].copy()
    from sklearn.preprocessing import MultiLabelBinarizer
    mlb = load(artifacts / "labels_mlb.joblib")
    Y = mlb.transform(df["label_list"])

    # Train/val split must be identical to training for fair comparison
    from sklearn.model_selection import train_test_split
    X_train, X_val, Y_train, Y_val = train_test_split(
        X, Y, test_size=0.2, random_state=RANDOM_STATE, stratify=(Y.sum(axis=1) > 0)
    )

    model = load(artifacts / "model.joblib")
    Y_prob = model.predict_proba(X_val)

    # Threshold sweep
    thresholds = [0.2, 0.3, 0.4, 0.5]
    rows = []
    for th in thresholds:
        Y_pred = (Y_prob >= th).astype(int)
        micro_f1 = f1_score(Y_val, Y_pred, average="micro", zero_division=0)
        macro_f1 = f1_score(Y_val, Y_pred, average="macro", zero_division=0)
        p_at3 = precision_at_k(Y_val, Y_prob, k=3)
        rows.append({"threshold": th, "micro_f1": micro_f1, "macro_f1": macro_f1, "precision_at_3": p_at3})

    pd.DataFrame(rows).to_csv(artifacts / "threshold_sweep.csv", index=False)
    print("Wrote artifacts/threshold_sweep.csv")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Test the updated extract_lab.py with the 001_labs.pdf file
"""

from pathlib import Path
from extract_lab import process_pdf

def main():
    test_file = Path("artifacts/001_labs.pdf")
    output_dir = Path("test_outputs")
    
    if not test_file.exists():
        print(f"Test file not found: {test_file}")
        return
    
    print(f"Testing extract_lab.py with: {test_file}")
    print("=" * 50)
    
    # Test with explode_desc=False
    print("Testing with explode_desc=False...")
    result_path = process_pdf(test_file, output_dir, explode_desc=False)
    
    if result_path:
        print(f"✓ SUCCESS: Extracted to {result_path}")
        
        # Show the first few lines of the output
        import pandas as pd
        df = pd.read_csv(result_path)
        print(f"  Shape: {df.shape}")
        print(f"  Columns: {list(df.columns)}")
        print("\nFirst 5 rows:")
        print(df.head().to_string())
        
        # Check if description_json column exists
        if 'description_json' in df.columns:
            print(f"\nDescription JSON sample:")
            print(df['description_json'].iloc[0])
    else:
        print("✗ FAILED: No output generated")
    
    print("\n" + "=" * 50)
    
    # Test with explode_desc=True
    print("Testing with explode_desc=True...")
    result_path2 = process_pdf(test_file, output_dir, explode_desc=True)
    
    if result_path2:
        print(f"✓ SUCCESS: Extracted to {result_path2}")
        
        # Show the columns to see if description was exploded
        import pandas as pd
        df2 = pd.read_csv(result_path2)
        print(f"  Shape: {df2.shape}")
        print(f"  Columns: {list(df2.columns)}")
    else:
        print("✗ FAILED: No output generated")


if __name__ == "__main__":
    main()

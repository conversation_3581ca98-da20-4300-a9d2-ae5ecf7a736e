#!/usr/bin/env python3

from pathlib import Path
from config import LAB_DIR, LAB_NORMALIZATION
import importlib.util
import sys

# Import from 01_extract.py
spec = importlib.util.spec_from_file_location("extract", "01_extract.py")
extract_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(extract_module)

_read_any_text = extract_module._read_any_text
parse_labs = extract_module.parse_labs
_normalize_key = extract_module._normalize_key
_find_canonical_key = extract_module._find_canonical_key
_to_number = extract_module._to_number
import re

def debug_single_file(file_path: Path):
    """Debug the extraction process for a single file"""
    print(f"\n=== Debugging file: {file_path.name} ===")
    
    # Read the raw text
    text = _read_any_text(file_path)
    print(f"Raw text length: {len(text)} characters")
    print(f"First 500 characters:")
    print("-" * 50)
    print(text[:500])
    print("-" * 50)
    
    # Show some lines for analysis
    lines = [ln.strip() for ln in text.splitlines() if ln.strip()]
    print(f"\nTotal non-empty lines: {len(lines)}")
    print(f"First 10 lines:")
    for i, line in enumerate(lines[:10]):
        print(f"  {i+1:2d}: {line}")
    
    # Test the parsing function
    print(f"\n=== Testing parse_labs function ===")
    feats = parse_labs(text)
    print(f"Extracted features: {feats}")
    
    # Debug line by line parsing
    print(f"\n=== Line-by-line analysis ===")
    for i, ln in enumerate(lines[:20]):  # Check first 20 lines
        print(f"\nLine {i+1}: '{ln}'")
        
        # Test table-like parsing
        parts = re.split(r"\s{2,}|\t", ln)
        if len(parts) >= 2:
            print(f"  Split into {len(parts)} parts: {parts}")
            nums = [p for p in parts if _to_number(p) is not None]
            if nums:
                print(f"  Found numbers: {nums}")
                result = _to_number(nums[0])
                print(f"  First number as result: {result}")
            key_candidate = parts[0]
            canon = _find_canonical_key(key_candidate)
            print(f"  Key candidate: '{key_candidate}' -> canonical: '{canon}'")
        
        # Test key:value parsing
        m = re.match(r"(.+?):\s*(.+)$", ln)
        if m:
            key_part = m.group(1)
            val_part = m.group(2)
            canon = _find_canonical_key(key_part)
            val = _to_number(val_part)
            print(f"  Key:Value format - Key: '{key_part}' -> canonical: '{canon}', Value: '{val_part}' -> number: {val}")

def debug_normalization():
    """Debug the normalization process"""
    print(f"\n=== Testing normalization functions ===")
    
    # Test some common lab names
    test_names = [
        "Hemoglobin (Hb)",
        "Hematocrit",
        "White Blood Cell Count",
        "Glucose",
        "Creatinine",
        "Sodium",
        "Age"
    ]
    
    for name in test_names:
        normalized = _normalize_key(name)
        canonical = _find_canonical_key(name)
        print(f"'{name}' -> normalized: '{normalized}' -> canonical: '{canonical}'")

def main():
    # Debug normalization first
    debug_normalization()
    
    # Debug a sample file
    sample_file = LAB_DIR / "001_labs.pdf"
    if sample_file.exists():
        debug_single_file(sample_file)
    else:
        print(f"Sample file not found: {sample_file}")

if __name__ == "__main__":
    main()

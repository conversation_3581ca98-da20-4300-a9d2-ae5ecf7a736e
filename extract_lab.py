#!/usr/bin/env python3
# extract_lab.py
# Fast Docling-based extractor for lab PDFs:
# - Finds the single (possibly multi-page) table
# - Extracts description text before first table
# - Writes per-file CSV with an extra 'description_json' column
#
# Usage:
#   python extract_lab.py [lab_files_path] [out_dir] [explode_desc]
#   If no arguments provided, uses LAB_DIR from config.py
#
# Notes:
# - Requires: pip install -U docling docling-ibm-models pandas
# - Docling refs (APIs & examples):
#   - PyPI: docling (SDK & CLI)           -> https://pypi.org/project/docling/
#   - Examples: table export/custom conv.  -> https://docling-project.github.io/docling/examples/
#   - Concepts / data model                -> https://docling-project.github.io/docling/concepts/

import json
import sys
from pathlib import Path
from typing import List, Optional, Tuple

import pandas as pd

# Docling high-level converter & options
from docling.document_converter import DocumentConverter, PdfFormatOption
from docling.datamodel.pipeline_options import PdfPipelineOptions
from docling.datamodel.base_models import InputFormat

# Import configuration
from config import LAB_DIR

# Some newer Docling versions can accelerate parts of the pipeline.
# This code keeps defaults to stay simple & portable.


def normalize_bool(s: str) -> bool:
    return str(s).strip().lower() in {"1", "true", "yes", "y"}


def pick_main_table(dfs: List[pd.DataFrame]) -> Optional[pd.DataFrame]:
    """
    Heuristic: pick the largest DataFrame (rows * cols) as the 'main' table.
    """
    if not dfs:
        return None
    dfs_scored = [(df, (df.shape[0] * df.shape[1])) for df in dfs]
    dfs_scored.sort(key=lambda x: x[1], reverse=True)
    return dfs_scored[0][0]


def coalesce_multi_page_table(dfs: List[pd.DataFrame]) -> Optional[pd.DataFrame]:
    """
    If multiple tables belong to the same logical table spanning pages,
    concatenating them top-to-bottom is usually reasonable. When uncertain,
    fall back to the single largest table.
    """
    if not dfs:
        return None

    # Try to align headers:
    # - If multiple frames share identical headers, stack them.
    # - Else choose the main table only.
    # Normalize header row by lowering/stripping.
    def header_key(df: pd.DataFrame) -> Tuple:
        cols = [str(c).strip().lower() for c in df.columns]
        return tuple(cols)

    # Group by header signature
    groups = {}
    for df in dfs:
        # If Docling didn't set headers, try first row as header
        if any(str(c).startswith("Unnamed") for c in df.columns) and len(df) > 0:
            df = df.copy()
            df.columns = df.iloc[0].astype(str).tolist()
            df = df.iloc[1:].reset_index(drop=True)

        groups.setdefault(header_key(df), []).append(df)

    # Pick the biggest compatible group
    best_group = max(groups.values(), key=lambda g: sum(d.shape[0] * d.shape[1] for d in g))

    if len(best_group) == 1:
        # Return the single best, or the largest overall if headers differ wildly
        return pick_main_table(dfs)

    # Vertically concatenate rows
    try:
        stacked = pd.concat(best_group, axis=0, ignore_index=True)
        return stacked
    except Exception:
        # If concat fails due to schema mismatch, return the largest table
        return pick_main_table(dfs)


def parse_kv_from_description(desc_text: str) -> dict:
    """
    Extract very lightweight key:value pairs from description text.
    Example lines:
        Patient ID: 12345
        Collected on: 2025-08-12
    """
    kv = {}
    for raw_line in desc_text.splitlines():
        line = raw_line.strip()
        if not line or ":" not in line:
            continue
        k, v = line.split(":", 1)
        k = k.strip()
        v = v.strip()
        if k and v:
            kv[k] = v
    return kv


def docling_convert_pdf(pdf_path: Path):
    """
    Run Docling conversion on a single PDF and return:
      - the structured document
      - list of pandas DataFrames for all extracted tables
      - a text blob containing description before the first table
    """
    # Configure PDF pipeline with table extraction on.
    pdf_pipeline_options = PdfPipelineOptions()  # defaults are fine for most programmatic PDFs
    converter = DocumentConverter(
        format_options={
            InputFormat.PDF: PdfFormatOption(pipeline_options=pdf_pipeline_options)
        }
    )

    # Convert the document
    result = converter.convert(pdf_path)
    doc = result.document  # DoclingDocument-like model with structured elements

    # Collect all tables as DataFrames
    # Docling exposes table export helpers via the document model; in recent versions,
    # tables can be exported to pandas with a simple call per table.
    dfs = []
    first_table_seen = False
    description_chunks: List[str] = []

    # Walk the high-level element stream in order; stop adding description when hitting the first table.
    # Depending on Docling version, use doc.elements or doc.sections/blocks; keeping this robust:
    elements = getattr(doc, "elements", None)
    if elements is None:
        # Fallback: build a flat list from sections/pages if needed
        # (Keeping it simple—Docling's public model always lets you iterate content in reading order.)
        elements = []
        for sec in getattr(doc, "sections", []):
            elements.extend(getattr(sec, "children", []))

    for el in elements:
        t = getattr(el, "type", "").lower()
        if "table" in t:
            # Export table to pandas
            # Many Docling builds expose el.export_to_dataframe()/to_pandas() or a similar helper.
            # Try common names; fallback to the document-level table exporter if present.
            df = None
            for candidate in ("to_pandas", "to_dataframe", "export_to_dataframe"):
                if hasattr(el, candidate):
                    try:
                        df = getattr(el, candidate)()
                        break
                    except Exception:
                        pass
            # Document-level table export (some examples show doc.tables -> pandas):
            if df is None and hasattr(doc, "tables"):
                # doc.tables may be a list of table objects with similar methods
                for tbl in getattr(doc, "tables", []):
                    if hasattr(tbl, "page_no") and hasattr(el, "page_no") and tbl.page_no != el.page_no:
                        continue
                    for candidate in ("to_pandas", "to_dataframe", "export_to_dataframe"):
                        if hasattr(tbl, candidate):
                            try:
                                df = getattr(tbl, candidate)()
                                break
                            except Exception:
                                pass
                    if df is not None:
                        break

            if df is not None and isinstance(df, pd.DataFrame) and not df.empty:
                dfs.append(df)

            first_table_seen = True
            # Do not collect description after the first table
        else:
            # Textual element before first table counts as description
            if not first_table_seen:
                txt = None
                for attr in ("text", "content", "value"):
                    if hasattr(el, attr):
                        txt = getattr(el, attr)
                        break
                if isinstance(txt, str) and txt.strip():
                    description_chunks.append(txt.strip())

    description_text = "\n".join(description_chunks).strip()

    return doc, dfs, description_text


def process_pdf(pdf_path: Path, out_dir: Path, explode_desc: bool) -> Optional[Path]:
    try:
        _, dfs, description_text = docling_convert_pdf(pdf_path)
    except Exception as e:
        print(f"[WARN] Docling failed on {pdf_path.name}: {e}", file=sys.stderr)
        return None

    if not dfs:
        print(f"[WARN] No tables found in {pdf_path.name}. Skipping.")
        return None

    # Merge pages/partials into one logical table
    table_df = coalesce_multi_page_table(dfs)
    if table_df is None or table_df.empty:
        print(f"[WARN] Unable to assemble table for {pdf_path.name}.")
        return None

    # Attach description as JSON column
    kv = parse_kv_from_description(description_text)
    table_df = table_df.copy()
    table_df["description_json"] = json.dumps(kv if kv else {"raw": description_text}, ensure_ascii=False)

    # Optionally explode parsed key/values into columns
    if explode_desc and kv:
        for k, v in kv.items():
            safe_col = k.strip().replace("\n", " ").replace("\t", " ")
            if safe_col:
                table_df[safe_col] = v

    # Write CSV
    out_dir.mkdir(parents=True, exist_ok=True)
    out_path = out_dir / (pdf_path.stem + ".csv")
    table_df.to_csv(out_path, index=False)
    return out_path


def main(lab_files_path: Optional[Path] = None, out_dir: Optional[Path] = None, explode_desc: bool = False):
    """
    Main function to extract lab PDFs to CSV.

    Args:
        lab_files_path: Path to directory containing lab PDFs. Defaults to LAB_DIR from config.py
        out_dir: Output directory for CSV files. Defaults to "outputs"
        explode_desc: Whether to explode description key/value pairs into separate columns
    """
    # Use defaults if not provided
    if lab_files_path is None:
        lab_files_path = LAB_DIR
    if out_dir is None:
        out_dir = Path("outputs")

    in_dir = Path(lab_files_path)
    out_dir = Path(out_dir)

    if not in_dir.exists():
        print(f"[ERR] Input directory does not exist: {in_dir}", file=sys.stderr)
        sys.exit(1)

    pdfs = sorted([p for p in in_dir.glob("**/*") if p.suffix.lower() in {".pdf"}])
    if not pdfs:
        print(f"[ERR] No PDFs found under {in_dir}", file=sys.stderr)
        sys.exit(2)

    print(f"[INFO] Found {len(pdfs)} PDF(s). Processing...")
    written = 0
    for pdf in pdfs:
        out_path = process_pdf(pdf, out_dir, explode_desc)
        if out_path:
            print(f"[OK] {pdf.name} -> {out_path}")
            written += 1

    print(f"[DONE] Wrote {written} CSV file(s) to {out_dir}")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# extract_lab.py
# Fast Docling-based extractor for lab PDFs:
# - Finds the single (possibly multi-page) table
# - Extracts description text before first table
# - Writes per-file CSV with an extra 'description_json' column
#
# Usage:
#   python extract_lab.py [lab_files_path] [out_dir] [explode_desc]
#   If no arguments provided, uses LAB_DIR from config.py
#
# Notes:
# - Requires: pip install -U docling docling-ibm-models pandas
# - Docling refs (APIs & examples):
#   - PyPI: docling (SDK & CLI)           -> https://pypi.org/project/docling/
#   - Examples: table export/custom conv.  -> https://docling-project.github.io/docling/examples/
#   - Concepts / data model                -> https://docling-project.github.io/docling/concepts/

import json
import sys
from pathlib import Path
from typing import List, Optional, Tuple

import pandas as pd

# Docling high-level converter & options
from docling.document_converter import DocumentConverter, PdfFormatOption
from docling.datamodel.pipeline_options import PdfPipelineOptions
from docling.datamodel.base_models import InputFormat

# Import configuration
from config import LAB_DIR

# Some newer Docling versions can accelerate parts of the pipeline.
# This code keeps defaults to stay simple & portable.


def normalize_bool(s: str) -> bool:
    return str(s).strip().lower() in {"1", "true", "yes", "y"}


def pick_main_table(dfs: List[pd.DataFrame]) -> Optional[pd.DataFrame]:
    """
    Heuristic: pick the largest DataFrame (rows * cols) as the 'main' table.
    """
    if not dfs:
        return None
    dfs_scored = [(df, (df.shape[0] * df.shape[1])) for df in dfs]
    dfs_scored.sort(key=lambda x: x[1], reverse=True)
    return dfs_scored[0][0]


def coalesce_multi_page_table(dfs: List[pd.DataFrame]) -> Optional[pd.DataFrame]:
    """
    If multiple tables belong to the same logical table spanning pages,
    concatenating them top-to-bottom is usually reasonable. When uncertain,
    fall back to the single largest table.
    """
    if not dfs:
        return None

    # Try to align headers:
    # - If multiple frames share identical headers, stack them.
    # - Else choose the main table only.
    # Normalize header row by lowering/stripping.
    def header_key(df: pd.DataFrame) -> Tuple:
        cols = [str(c).strip().lower() for c in df.columns]
        return tuple(cols)

    # Group by header signature
    groups = {}
    for df in dfs:
        # If Docling didn't set headers, try first row as header
        if any(str(c).startswith("Unnamed") for c in df.columns) and len(df) > 0:
            df = df.copy()
            df.columns = df.iloc[0].astype(str).tolist()
            df = df.iloc[1:].reset_index(drop=True)

        groups.setdefault(header_key(df), []).append(df)

    # Pick the biggest compatible group
    best_group = max(groups.values(), key=lambda g: sum(d.shape[0] * d.shape[1] for d in g))

    if len(best_group) == 1:
        # Return the single best, or the largest overall if headers differ wildly
        return pick_main_table(dfs)

    # Vertically concatenate rows
    try:
        stacked = pd.concat(best_group, axis=0, ignore_index=True)
        return stacked
    except Exception:
        # If concat fails due to schema mismatch, return the largest table
        return pick_main_table(dfs)


def parse_kv_from_description(desc_text: str) -> dict:
    """
    Extract key:value pairs from description text, handling both single-line and multi-line formats.
    Examples:
        Single line: Age: 45
        Multi-line:  Patient Name:
                     Rose Montgomery
    """
    kv = {}
    lines = desc_text.splitlines()
    i = 0

    while i < len(lines):
        line = lines[i].strip()

        # Skip empty lines and headers (starting with #)
        if not line or line.startswith('#'):
            i += 1
            continue

        # Check if this line has a colon (potential key)
        if ":" in line:
            k, v = line.split(":", 1)
            k = k.strip()
            v = v.strip()

            # Clean up key (remove markdown formatting)
            k = k.replace('#', '').strip()

            # If value is empty, look for it on the next non-empty line
            if not v and i + 1 < len(lines):
                next_i = i + 1
                while next_i < len(lines):
                    next_line = lines[next_i].strip()
                    if next_line and not next_line.startswith('#'):
                        # Check if next line is another key (has colon) - if so, stop
                        if ':' in next_line and not next_line.startswith('http'):
                            break
                        v = next_line
                        i = next_i  # Skip the value line
                        break
                    next_i += 1

            # Store the key-value pair if both exist
            if k and v:
                kv[k] = v

        i += 1

    return kv


def docling_convert_pdf(pdf_path: Path):
    """
    Run Docling conversion on a single PDF and return:
      - the structured document
      - list of pandas DataFrames for all extracted tables
      - a text blob containing description before the first table
    """
    # Configure PDF pipeline with table extraction on.
    pdf_pipeline_options = PdfPipelineOptions()  # defaults are fine for most programmatic PDFs
    converter = DocumentConverter(
        format_options={
            InputFormat.PDF: PdfFormatOption(pipeline_options=pdf_pipeline_options)
        }
    )

    # Convert the document
    result = converter.convert(pdf_path)
    doc = result.document  # DoclingDocument-like model with structured elements

    # Extract tables directly from doc.tables (current API)
    dfs = []
    if hasattr(doc, "tables") and doc.tables:
        for tbl in doc.tables:
            try:
                # Use export_to_dataframe method with doc parameter to avoid deprecation warning
                df = tbl.export_to_dataframe(doc)
                if df is not None and isinstance(df, pd.DataFrame) and not df.empty:
                    dfs.append(df)
            except Exception as e:
                # Fallback to deprecated method if new one fails
                try:
                    df = tbl.export_to_dataframe()
                    if df is not None and isinstance(df, pd.DataFrame) and not df.empty:
                        dfs.append(df)
                except Exception:
                    print(f"[WARN] Failed to export table: {e}", file=sys.stderr)

    # Extract description text from the document
    # For now, we'll use a simple approach to get text content before tables
    description_text = ""

    # Try to get text content from the document
    if hasattr(doc, 'export_to_markdown'):
        try:
            # Get full markdown and extract text before first table
            full_text = doc.export_to_markdown()
            # Simple heuristic: take text before first table-like pattern
            lines = full_text.split('\n')
            description_lines = []
            for line in lines:
                # Stop at first table-like content (lines with | or multiple consecutive spaces)
                if '|' in line or (len(line.split()) > 3 and '  ' in line):
                    break
                if line.strip() and not line.startswith('#'):
                    description_lines.append(line.strip())
            description_text = '\n'.join(description_lines).strip()
        except Exception:
            description_text = ""

    return doc, dfs, description_text


def process_pdf(pdf_path: Path, out_dir: Path, explode_desc: bool) -> Optional[Path]:
    try:
        _, dfs, description_text = docling_convert_pdf(pdf_path)
    except Exception as e:
        print(f"[WARN] Docling failed on {pdf_path.name}: {e}", file=sys.stderr)
        return None

    if not dfs:
        print(f"[WARN] No tables found in {pdf_path.name}. Skipping.")
        return None

    # Merge pages/partials into one logical table
    table_df = coalesce_multi_page_table(dfs)
    if table_df is None or table_df.empty:
        print(f"[WARN] Unable to assemble table for {pdf_path.name}.")
        return None

    # Attach description as JSON column
    kv = parse_kv_from_description(description_text)
    table_df = table_df.copy()
    table_df["description_json"] = json.dumps(kv if kv else {"raw": description_text}, ensure_ascii=False)

    # Optionally explode parsed key/values into columns
    if explode_desc and kv:
        for k, v in kv.items():
            safe_col = k.strip().replace("\n", " ").replace("\t", " ")
            if safe_col:
                table_df[safe_col] = v

    # Write CSV
    out_dir.mkdir(parents=True, exist_ok=True)
    out_path = out_dir / (pdf_path.stem + ".csv")
    table_df.to_csv(out_path, index=False)
    return out_path


def main(lab_files_path: Optional[Path] = None, out_dir: Optional[Path] = None, explode_desc: bool = False):
    """
    Main function to extract lab PDFs to CSV.

    Args:
        lab_files_path: Path to directory containing lab PDFs. Defaults to LAB_DIR from config.py
        out_dir: Output directory for CSV files. Defaults to "processed" folder within lab_files_path
        explode_desc: Whether to explode description key/value pairs into separate columns
    """
    # Use defaults if not provided
    if lab_files_path is None:
        lab_files_path = LAB_DIR

    in_dir = Path(lab_files_path)

    # Default output directory is "processed" folder within the lab files directory
    if out_dir is None:
        out_dir = in_dir / "processed"
    else:
        out_dir = Path(out_dir)

    if not in_dir.exists():
        print(f"[ERR] Input directory does not exist: {in_dir}", file=sys.stderr)
        sys.exit(1)

    pdfs = sorted([p for p in in_dir.glob("**/*") if p.suffix.lower() in {".pdf"}])
    if not pdfs:
        print(f"[ERR] No PDFs found under {in_dir}", file=sys.stderr)
        sys.exit(2)

    print(f"[INFO] Found {len(pdfs)} PDF(s). Processing...")

    # Check which files already exist and skip them
    to_process = []
    skipped = 0

    for pdf in pdfs:
        expected_csv = out_dir / (pdf.stem + ".csv")
        if expected_csv.exists():
            print(f"[SKIP] {pdf.name} -> {expected_csv.name} (already exists)")
            skipped += 1
        else:
            to_process.append(pdf)

    if skipped > 0:
        print(f"[INFO] Skipped {skipped} already processed file(s)")

    if not to_process:
        print(f"[INFO] All files already processed. Nothing to do.")
        return

    print(f"[INFO] Processing {len(to_process)} new file(s)...")
    written = 0

    for pdf in to_process:
        out_path = process_pdf(pdf, out_dir, explode_desc)
        if out_path:
            print(f"[OK] {pdf.name} -> {out_path}")
            written += 1

    print(f"[DONE] Wrote {written} new CSV file(s) to {out_dir}")
    if skipped > 0:
        print(f"[INFO] Total files in processed folder: {written + skipped}")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3

from pathlib import Path
from config import LAB_DIR
import importlib.util

# Import from 01_extract.py
spec = importlib.util.spec_from_file_location("extract", "01_extract.py")
extract_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(extract_module)

_read_any_text = extract_module._read_any_text

def debug_raw_text():
    """Debug the raw text extraction"""
    sample_file = LAB_DIR / "001_labs.pdf"
    if not sample_file.exists():
        print(f"Sample file not found: {sample_file}")
        return
    
    text = _read_any_text(sample_file)
    
    # Find the cholesterol section and show raw characters
    lines = text.splitlines()
    for i, line in enumerate(lines):
        if 'cholesterol' in line.lower():
            print(f"Raw line {i+1}: {repr(line)}")
            print(f"Displayed: {line}")
            
            # Show character by character for the problematic part
            print("Character analysis:")
            for j, char in enumerate(line):
                if j > 50:  # Focus on the part with the value
                    print(f"  {j:2d}: '{char}' (ord: {ord(char)})")
            print()

def main():
    debug_raw_text()

if __name__ == "__main__":
    main()

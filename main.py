# main.py
import argparse
import json
import sys
from pathlib import Path

# Import the step scripts (must be in the same directory)
import importlib

def _import_step(module_name, func_name="main"):
    try:
        mod = importlib.import_module(module_name)
        fn = getattr(mod, func_name)
        if not callable(fn):
            raise AttributeError(f"{module_name}.{func_name} is not callable")
        return fn
    except Exception as e:
        print(f"[FATAL] Could not import {module_name}: {e}", file=sys.stderr)
        sys.exit(1)

def run_extract():
    print("\n[1/3] Extract → artifacts/dataset.csv")
    step = _import_step("01_extract")
    step()

def run_train():
    print("\n[2/3] Train → artifacts/model.joblib, artifacts/labels_mlb.joblib, artifacts/metrics.json")
    step = _import_step("02_train")
    step()

def run_validate():
    print("\n[3/3] Validate (threshold sweep) → artifacts/threshold_sweep.csv")
    step = _import_step("03_validate")
    step()

def maybe_run_infer(lab_path: Path | None):
    if not lab_path:
        return
    print(f"\n[+] Inference preview for: {lab_path}")
    # Call 04_infer.main with the path as CLI arg (it expects sys.argv)
    # We’ll import and call its main(), temporarily patching sys.argv.
    infer_main = _import_step("04_infer")
    old_argv = sys.argv[:]
    try:
        sys.argv = ["04_infer.py", str(lab_path)]
        infer_main()
    finally:
        sys.argv = old_argv

def print_metrics_summary():
    metrics_path = Path("artifacts/metrics.json")
    if not metrics_path.exists():
        print("\n[WARN] metrics.json not found; skipping summary.")
        return
    try:
        data = json.loads(metrics_path.read_text())
        micro_f1 = data.get("micro_f1")
        macro_f1 = data.get("macro_f1")
        micro_auc = data.get("micro_auc")
        classes = data.get("classes", [])
        print("\n=== Validation Summary ===")
        if micro_f1 is not None:
            print(f"micro F1 : {micro_f1:.3f}")
        if macro_f1 is not None:
            print(f"macro F1 : {macro_f1:.3f}")
        if micro_auc is not None:
            print(f"micro AUC: {micro_auc:.3f}")
        print(f"#classes : {len(classes)}")
        print("Artifacts written under ./artifacts/")
    except Exception as e:
        print(f"[WARN] Failed to read metrics.json: {e}")

def main():
    parser = argparse.ArgumentParser(
        description="End-to-end pipeline: extract → train → validate → (optional) infer"
    )
    parser.add_argument(
        "--skip-extract", action="store_true",
        help="Skip dataset extraction step"
    )
    parser.add_argument(
        "--skip-validate", action="store_true",
        help="Skip threshold sweep validation step"
    )
    parser.add_argument(
        "--infer", type=str, default=None,
        help="Optional: path to a lab file (e.g., files/labs/123.pdf) to run a quick prediction preview"
    )
    args = parser.parse_args()

    # Ensure expected folders
    Path("artifacts").mkdir(exist_ok=True)

    if not args.skip_extract:
        run_extract()
    else:
        print("[skip] Extract")

    run_train()

    if not args.skip_validate:
        run_validate()
    else:
        print("[skip] Validate")

    print_metrics_summary()

    if args.infer:
        maybe_run_infer(Path(args.infer))

if __name__ == "__main__":
    main()
